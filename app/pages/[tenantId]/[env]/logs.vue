<script setup lang="ts">
import { debounce } from 'lodash'
import { sub } from 'date-fns'
import type BaseDateRangePicker from '~/components/base/BaseDateRangePicker.vue'
import type { Mail } from '~/types'

const { selectedTenantId, selectedEnv } = useApp()
const logsStore = useLogsStore()
const {
  logsFilter,
  logsSubFilter,
  selectedLog,
  logsFilterKeyword,
  logsFiltered,
  logsFilteredUniqueBySessionId,
  allRelatedLogs,
  loadings,
  logPagination,
  logPaginationTotal
} = storeToRefs(logsStore)
const debounceRefresh = debounce(async () => {
  await logsStore.searchLogs(
    selectedTenantId.value,
    selectedEnv.value?.environment || 0
  )
}, 1000)
const selectedMode = ref(0)

const messageSelectedLogs = ref<any[]>([])
const sessionSelectedLogs = ref<any[]>([])

// Watch for mode changes to ensure checkbox state is updated
watch(selectedMode, () => {
  // Force reactivity update for checkbox states when mode changes
  nextTick()
})
// const dropdownItems = [
//   [
//     {
//       label: '重要としてマーク',
//       icon: 'i-heroicons-exclamation-circle'
//     }
//   ],
//   [
//     {
//       label: '削除',
//       icon: 'i-heroicons-trash-20-solid'
//     }
//   ]
// ]

const selectedMail = ref<Mail | null>()
const showFilter = ref(false)
const showAdvancedSearch = ref(false)
const isMailPanelOpen = computed({
  get() {
    return !!selectedMail.value
  },
  set(value: boolean) {
    if (!value) {
      selectedMail.value = null
    }
  }
})
watch(
  () => [
    logsSubFilter.value.context_type,
    logsSubFilter.value.analyzed_action,
    logsSubFilter.value.processed
  ],
  async () => {
    await logsStore.searchLogs(
      selectedTenantId.value,
      selectedEnv.value?.environment || 0
    )
  }
)
watch(
  () => [
    logsSubFilter.value.answer,
    logsSubFilter.value.query,
    logsSubFilter.value.request_id,
    logsSubFilter.value.category_id,
    logsSubFilter.value.session_id
  ],
  async () => {
    await debounceRefresh()
  }
)
// watch logsFilter changes
watch(
  () => logsFilter.value,
  () => {
    logsStore.searchLogs(
      selectedTenantId.value,
      selectedEnv.value?.environment || 0
    )
    // Only clear selected logs when filter changes, not when pagination changes
    messageSelectedLogs.value = []
    sessionSelectedLogs.value = []
  },
  {
    deep: true,
    immediate: true
  }
)
watch(
  () => logPagination.value.pageCount,
  () => {
    logPagination.value.page = 1
  }
)

watch(
  () => logPagination.value,
  () => {
    logsStore.searchLogs(
      selectedTenantId.value,
      selectedEnv.value?.environment || 0
    )
    // Remove clearing of selected logs when pagination changes
    // This allows keeping selected items across pages
  },
  { deep: true, immediate: true }
)
const showLogsFilterKeyword = ref(true)
const keywordFilterRef = ref<any | null>(null)
watch(
  () => showLogsFilterKeyword.value,
  () => {
    if (showLogsFilterKeyword.value) {
      nextTick(() => {
        keywordFilterRef.value?.input?.focus()
      })
    }
  }
)

defineShortcuts({
  '/': () => {
    keywordFilterRef.value?.input?.focus()
  }
})

const tabItems = [
  {
    label: 'メッセージ単位'
  },
  {
    label: 'セッション単位'
  }
]

const logs = computed(() => {
  if (selectedMode.value === 0) {
    return logsFiltered.value
  } else {
    return logsFilteredUniqueBySessionId.value
  }
})
const exportSelectedLogsToCSV = (rows: any) => {
  if (selectedMode.value === 0) {
    logsStore.exportLogsToCSV([rows], rows.query)
  } else {
    const filteredLog = allRelatedLogs.value
      .filter(log => log.session_id === rows.session_id)
      .sort(
        (a, b) =>
          new Date(a.query_created_at).getTime()
            - new Date(b.query_created_at).getTime()
      )
    logsStore.exportLogsToCSV(filteredLog, `session_unit_logs`)
  }
}

const currentSelectedLogs = computed({
  get() {
    return selectedMode.value === 0
      ? messageSelectedLogs.value
      : sessionSelectedLogs.value
  },
  set(newValue) {
    if (selectedMode.value === 0) {
      messageSelectedLogs.value = newValue
    } else {
      sessionSelectedLogs.value = newValue
    }
  }
})

// Check if all items in current page are selected
const isAllCurrentPageSelected = computed(() => {
  if (logs.value.length === 0) return false

  return logs.value.every((log) => {
    if (selectedMode.value === 0) {
      return messageSelectedLogs.value.some(
        selected => selected.request_id === log.request_id
      )
    } else {
      return sessionSelectedLogs.value.some(
        selected => selected.session_id === log.session_id
      )
    }
  })
})

// Check if some items in current page are selected (for indeterminate state)
const isSomeCurrentPageSelected = computed(() => {
  if (logs.value.length === 0) return false

  return logs.value.some((log) => {
    if (selectedMode.value === 0) {
      return messageSelectedLogs.value.some(
        selected => selected.request_id === log.request_id
      )
    } else {
      return sessionSelectedLogs.value.some(
        selected => selected.session_id === log.session_id
      )
    }
  })
})

// Handle select all checkbox
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    // Add all current page items to selection
    if (selectedMode.value === 0) {
      // Message mode: add individual messages
      const currentLogs = messageSelectedLogs.value
      const newItems = logs.value.filter(
        log =>
          !currentLogs.some(
            selected => selected.request_id === log.request_id
          )
      )
      messageSelectedLogs.value = [...messageSelectedLogs.value, ...newItems]
    } else {
      // Session mode: add all logs for each session on current page
      const currentLogs = sessionSelectedLogs.value
      const newSessionItems: any[] = []

      logs.value.forEach((log) => {
        if (
          !currentLogs.some(
            selected => selected.session_id === log.session_id
          )
        ) {
          // Find all logs with the same session_id from logsFiltered
          const logsWithSameSession = logsFiltered.value.filter(
            l => l.session_id === log.session_id
          )
          newSessionItems.push(...logsWithSameSession)
        }
      })

      sessionSelectedLogs.value = [
        ...sessionSelectedLogs.value,
        ...newSessionItems
      ]
    }
  } else {
    // Remove all current page items from selection
    if (selectedMode.value === 0) {
      messageSelectedLogs.value = messageSelectedLogs.value.filter(
        selected =>
          !logs.value.some(log => log.request_id === selected.request_id)
      )
    } else {
      sessionSelectedLogs.value = sessionSelectedLogs.value.filter(
        selected =>
          !logs.value.some(log => log.session_id === selected.session_id)
      )
    }
  }
}

// Computed property for selected logs count
const selectedLogsCount = computed(() => {
  if (selectedMode.value === 0) {
    return messageSelectedLogs.value.length
  } else {
    // For session mode, count actual logs that will be exported
    // Get unique session IDs to avoid counting duplicates
    const uniqueSessionIds = new Set(
      sessionSelectedLogs.value.map(log => log.session_id)
    )
    return logsFiltered.value.filter(log =>
      uniqueSessionIds.has(String(log.session_id))
    ).length
  }
})

// Computed property for selected sessions count (for session mode display)
const selectedSessionsCount = computed(() => {
  if (selectedMode.value !== 1) return 0

  // Get unique session IDs to avoid counting duplicates
  const uniqueSessionIds = new Set(
    sessionSelectedLogs.value.map(log => log.session_id)
  )
  return uniqueSessionIds.size
})

// Computed property for chip display text
const chipDisplayText = computed(() => {
  if (selectedLogsCount.value === 0) return ''

  if (selectedMode.value === 0) {
    return selectedLogsCount.value.toString()
  } else {
    // For session mode, show sessions count with 'S' suffix to indicate sessions
    return `${selectedSessionsCount.value}S`
  }
})

const rowMenus = () => {
  const currentSelectedLogs
    = selectedMode.value === 0
      ? messageSelectedLogs.value
      : sessionSelectedLogs.value
  const menus = [
    [
      {
        // Export all logs
        label: 'すべてのログを出力',
        icon: 'ph:file-csv-light',
        click: () => {
          if (selectedMode.value === 0) {
            logsStore.exportLogsToCSV(logs.value, `message_unit_logs`)
          } else {
            logsStore.exportLogsToCSV(logsFiltered.value, `session_unit_logs`)
          }
        }
      },
      {
        // Export all logs on current page
        label: 'ページ内のログを出力',
        icon: 'ph:file-csv-light',
        click: () => {
          if (selectedMode.value === 0) {
            logsStore.exportLogsToCSV(logs.value, `message_unit_logs`)
          } else {
            logsStore.exportLogsToCSV(logsFiltered.value, `session_unit_logs`)
          }
        }
      }
    ]
  ]

  if (currentSelectedLogs.length > 0) {
    const menuLabel
      = selectedMode.value === 0
        ? `選択中のログのみ (${currentSelectedLogs.length})`
        : `選択中のログのみ (${selectedSessionsCount.value}セッション)`

    menus.push([
      {
        label: menuLabel,
        icon: 'ph:file-csv-light',
        click: () => {
          if (selectedMode.value === 0) {
            logsStore.exportLogsToCSV(
              messageSelectedLogs.value,
              `message_unit_logs`
            )
          } else {
            // Get unique session IDs to avoid duplicates
            const uniqueSessionIds = new Set(
              sessionSelectedLogs.value.map(log => log.session_id)
            )
            const filteredLogs = logsFiltered.value.filter(log =>
              uniqueSessionIds.has(String(log.session_id))
            )
            logsStore.exportLogsToCSV(filteredLogs, `session_unit_logs`)
          }
        }
      },
      {
        label: '選択解除',
        icon: 'material-symbols:deselect',
        click: () => {
          if (selectedMode.value === 0) {
            messageSelectedLogs.value = []
          } else {
            sessionSelectedLogs.value = []
          }
        }
      }
    ])
  }
  return menus
}
const defaultProcessedType = [
  { label: '正常', value: true },
  { label: '失敗', value: false }
]
const defaultAnalyzedActionType = [
  { label: '通常', value: 1 },
  { label: 'RAG', value: 2 },
  { label: '要約', value: 3 },
  { label: '天気', value: 101 },
  { label: 'セマンテックキャッシュ', value: 199 }
]
const contextTypes = [
  { label: '不明', value: 0 },
  { label: 'ナレッジデータベース', value: 1 },
  { label: 'ウエブ検索', value: 2 },
  { label: '見つからない', value: 99 }
]

// Advanced search functions
const onAdvancedSearch = (conditions: any) => {
  console.log('🚀 ~ onAdvancedSearch ~ conditions:', conditions)

  // Update logsFilter range
  logsFilter.value.range = conditions.range

  // Update logsSubFilter with search conditions
  logsSubFilter.value = {
    ...logsSubFilter.value,
    session_id: conditions.session_id || '',
    request_id: conditions.request_id || '',
    category_id: conditions.category_id || '',
    query: conditions.query || '',
    answer: conditions.answer || '',
    context_type: conditions.context_type,
    analyzed_action: conditions.analyzed_action,
    processed: conditions.processed
  }

  showAdvancedSearch.value = false
}

const onClearAdvancedSearch = () => {
  logsFilter.value = {
    range: { start: sub(new Date(), { days: 3 }), end: new Date() }
  }
  logsSubFilter.value = {}
}

// Check if any advanced search filter is applied
const hasAnyLogsFilter = computed(() => {
  return !!(
    logsSubFilter.value.session_id
    || logsSubFilter.value.request_id
    || logsSubFilter.value.category_id
    || logsSubFilter.value.query
    || logsSubFilter.value.answer
    || logsSubFilter.value.context_type
    || logsSubFilter.value.analyzed_action
    || logsSubFilter.value.processed
  )
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel
      id="logs"
      :width="600"
      :resizable="{ min: 400, max: 600 }"
    >
      <UDashboardNavbar
        title="ログ情報"
        :badge="logPaginationTotal"
      >
        <template #right>
          <UTabs
            v-model="selectedMode"
            :items="tabItems"
            :ui="{
              wrapper: '',
              list: {
                height: 'h-9',
                tab: { height: 'h-7', size: 'text-[13px]' }
              }
            }"
          />
        </template>
      </UDashboardNavbar>
      <UDashboardToolbar
        class="py-0 pl-2 dark:bg-gray-900/85 overflow-x-auto shadow-md"
      >
        <div class="flex items-center justify-between w-full gap-2">
          <div class="flex items-center gap-2">
            <UTooltip text="現在のページの全ての項目を選択/選択解除">
              <UButton
                :color="'white'"
                size="sm"
                :variant="'solid'"
              >
                <UCheckbox
                  :model-value="isAllCurrentPageSelected"
                  :indeterminate="
                    !isAllCurrentPageSelected && isSomeCurrentPageSelected
                  "
                  color="primary"
                  @update:model-value="handleSelectAll"
                />
              </UButton>
            </UTooltip>
            <BaseDateRangePicker
              v-model="logsFilter.range"
              size="sm"
              mini
            />
          </div>
          <UInput
            ref="keywordFilterRef"
            v-model="logsFilterKeyword"
            icon="mingcute:search-3-line"
            autocomplete="off"
            placeholder="キーワードフィルタ..."
            size="sm"
            class="flex-1"
            @keydown.esc="$event.target.blur()"
          >
            <template #trailing>
              <UKbd value="/" />
            </template>
          </UInput>

          <div class="flex items-center gap-2 justify-between">
            <UButton
              v-if="hasAnyLogsFilter"
              icon="ant-design:clear-outlined"
              color="gray"
              size="sm"
              @click="onClearAdvancedSearch"
            />
            <UChip
              size="lg"
              :show="hasAnyLogsFilter"
            >
              <UButton
                :icon="'mage:filter'"
                :color="hasAnyLogsFilter ? 'primary' : 'gray'"
                size="sm"
                :variant="hasAnyLogsFilter ? 'outline' : 'solid'"
                @click="showAdvancedSearch = !showAdvancedSearch"
              />
            </UChip>
            <UDropdown
              class="hidden group-hover:block z-50"
              :class="{
                block: true,
                hidden: false
              }"
              :items="rowMenus()"
              :popper="{ placement: 'bottom-start' }"
              :ui="{ width: 'w-60' }"
            >
              <UTooltip
                :text="
                  selectedLogsCount > 0
                    ? selectedMode === 0
                      ? `${selectedLogsCount}個のログが選択されています`
                      : `${selectedSessionsCount}セッション (${selectedLogsCount}ログ) が選択されています`
                    : 'ログをエクスポート'
                "
              >
                <UChip
                  size="lg"
                  :show="selectedLogsCount > 0"
                  :text="chipDisplayText"
                >
                  <UButton
                    class="row-menu"
                    :color="selectedLogsCount > 0 ? 'primary' : 'gray'"
                    :variant="selectedLogsCount > 0 ? 'outline' : 'solid'"
                    icon="tabler:file-download"
                    size="sm"
                    :loading="false"
                  />
                </UChip>
              </UTooltip>
            </UDropdown>

            <!-- <UButton
              :icon="showFilter ? 'material-symbols:hide-rounded' : 'mage:filter'"
              color="white"
              size="sm"
              label="簡易フィルタ"
              @click="showFilter = !showFilter"
            /> -->
          </div>
        </div>
      </UDashboardToolbar>
      <UDashboardToolbar
        v-if="showFilter"
        class="py-0 pl-2 dark:bg-gray-900/85 shadow-md"
      >
        <div class="flex-col gap-4 pt-2 w-full">
          <div class="flex items-center justify-between w-full gap-3 pb-2">
            <UInput
              v-model="logsSubFilter.answer"
              icon="mingcute:search-3-line"
              autocomplete="off"
              class="w-full"
              placeholder="回答検索..."
              size="sm"
              @keydown.esc="$event.target.blur()"
            >
              <template #trailing>
                <UKbd value="/" />
              </template>
            </UInput>
            <UInput
              v-model="logsSubFilter.query"
              icon="mingcute:search-3-line"
              autocomplete="off"
              class="w-full"
              placeholder="キューリー検索..."
              size="sm"
              @keydown.esc="$event.target.blur()"
            >
              <template #trailing>
                <UKbd value="/" />
              </template>
            </UInput>
          </div>
          <div class="flex items-center justify-between gap-3 pb-2">
            <UInput
              v-model="logsSubFilter.request_id"
              icon="mingcute:search-3-line"
              autocomplete="off"
              class="w-full"
              placeholder="リクエストID検索..."
              size="sm"
              @keydown.esc="$event.target.blur()"
            >
              <template #trailing>
                <UKbd value="/" />
              </template>
            </UInput>
            <UInput
              v-model="logsSubFilter.session_id"
              icon="mingcute:search-3-line"
              autocomplete="off"
              class="w-full"
              placeholder="セッションID検索..."
              size="sm"
              @keydown.esc="$event.target.blur()"
            >
              <template #trailing>
                <UKbd value="/" />
              </template>
            </UInput>
          </div>
          <div class="flex items-center justify-between w-full gap-3 pb-2">
            <UInput
              v-model="logsSubFilter.category_id"
              icon="mingcute:search-3-line"
              autocomplete="off"
              class="w-full"
              placeholder="カテゴリID検索..."
              size="sm"
              @keydown.esc="$event.target.blur()"
            >
              <template #trailing>
                <UKbd value="/" />
              </template>
            </UInput>
            <USelectMenu
              v-model="logsSubFilter.processed"
              icon="i-heroicons-check-circle"
              placeholder="正常処理済み"
              class="w-full"
              :options="defaultProcessedType"
              :ui-menu="{ option: { base: 'capitalize' } }"
            >
              <template #label>
                <div v-if="logsSubFilter.processed">
                  {{ logsSubFilter.processed.label }}
                </div>
                <div v-else>
                  正常処理済み
                </div>
              </template>
              <template
                v-if="logsSubFilter.processed"
                #trailing
              >
                <UButton
                  size="xs"
                  icon="i-lucide-delete"
                  color="gray"
                  :class="[
                    'ml-2 px-2 py-1 rounded hover:text-red-600 !pointer-events-auto'
                  ]"
                  @click.stop="
                    () => {
                      logsSubFilter.processed = null;
                    }
                  "
                />
              </template>
            </USelectMenu>
          </div>
          <div class="flex items-center justify-between w-full gap-3 pb-2">
            <USelectMenu
              v-model="logsSubFilter.analyzed_action"
              icon="i-heroicons-check-circle"
              placeholder="分析アクション"
              class="w-full"
              :options="defaultAnalyzedActionType"
              :ui-menu="{ option: { base: 'capitalize' } }"
            >
              <template #label>
                <div v-if="logsSubFilter.analyzed_action">
                  {{ logsSubFilter.analyzed_action.label }}
                </div>
                <div v-else>
                  分析アクション
                </div>
              </template>
              <template
                v-if="logsSubFilter.analyzed_action"
                #trailing
              >
                <UButton
                  size="xs"
                  icon="i-lucide-delete"
                  color="gray"
                  :class="[
                    'ml-2 px-2 py-1 rounded hover:text-red-600 !pointer-events-auto'
                  ]"
                  @click.stop="
                    () => {
                      logsSubFilter.analyzed_action = null;
                    }
                  "
                />
              </template>
            </USelectMenu>

            <USelectMenu
              v-model="logsSubFilter.context_type"
              icon="i-heroicons-check-circle"
              placeholder="ファイル種類"
              class="w-full"
              :options="contextTypes"
              :ui-menu="{ option: { base: 'capitalize' } }"
            >
              <template #label>
                <div v-if="logsSubFilter.context_type">
                  {{ logsSubFilter.context_type.label }}
                </div>
                <div v-else>
                  ファイル種類
                </div>
              </template>
              <template
                v-if="logsSubFilter.context_type"
                #trailing
              >
                <UButton
                  size="xs"
                  icon="i-lucide-delete"
                  color="gray"
                  :class="[
                    'ml-2 px-2 py-1 rounded hover:text-red-600 !pointer-events-auto'
                  ]"
                  @click.stop="
                    () => {
                      logsSubFilter.context_type = null;
                    }
                  "
                />
              </template>
            </USelectMenu>
          </div>
        </div>
      </UDashboardToolbar>
      <!-- ~/components/inbox/InboxList.vue -->
      <LogsList
        v-model="selectedLog"
        v-model:selected-logs="currentSelectedLogs"
        :mode="selectedMode"
        :logs="logs"
        :logs-filter-keyword="logsFilterKeyword"
        :loading="loadings.searchLogs"
        @export-selected-log="exportSelectedLogsToCSV"
      />

      <UDashboardToolbar>
        <template #left>
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>

            <USelect
              v-model="logPagination.pageCount"
              :options="[10, 20, 50, 100, 500, 1000]"
              class="w-20"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-model="logPagination.page"
            :page-count="logPagination.pageCount"
            :total="logPaginationTotal"
            size="sm"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>

    <UDashboardPanel
      v-model="isMailPanelOpen"
      collapsible
      grow
      side="right"
    >
      <template v-if="selectedLog">
        <UDashboardNavbar>
          <template #toggle>
            <UDashboardNavbarToggle icon="i-heroicons-x-mark" />

            <UDivider
              orientation="vertical"
              class="mx-1.5 lg:hidden"
            />
          </template>

          <template #left>
            <div class="text-sm font-semibold text-gray-900 dark:text-white">
              リクエストID: {{ (selectedLog as any)?.request_id }}
            </div>
            <!-- <UTooltip text="Archive">
              <UButton
                icon="i-heroicons-archive-box"
                color="gray"
                variant="ghost"
              />
            </UTooltip>

            <UTooltip text="Move to junk">
              <UButton
                icon="i-heroicons-archive-box-x-mark"
                color="gray"
                variant="ghost"
              />
            </UTooltip> -->

            <!-- <UDivider
              orientation="vertical"
              class="mx-1.5"
            />

            <UPopover :popper="{ placement: 'bottom-start' }">
              <template #default="{ open }">
                <UTooltip
                  text="Snooze"
                  :prevent="open"
                >
                  <UButton
                    icon="i-heroicons-clock"
                    color="gray"
                    variant="ghost"
                    :class="[open && 'bg-gray-50 dark:bg-gray-800']"
                  />
                </UTooltip>
              </template>

              <template #panel="{ close }">
                <DatePicker @close="close" />
              </template>
            </UPopover> -->
          </template>

          <template #right>
            <!-- <UDivider
              orientation="vertical"
              class="mx-1.5"
            />

            <UDropdown :items="dropdownItems">
              <UButton
                icon="i-heroicons-ellipsis-vertical"
                color="gray"
                variant="ghost"
              />
            </UDropdown> -->
          </template>
        </UDashboardNavbar>

        <!-- ~/components/inbox/InboxMail.vue -->
        <LogDetail
          :logs="selectedMode === 0 ? [selectedLog] : allRelatedLogs"
        />
      </template>
      <div
        v-else
        class="flex-1 hidden lg:flex items-center justify-center"
      >
        <UIcon
          name="fluent:people-chat-24-filled"
          class="w-28 h-32 text-gray-400 dark:text-gray-500"
        />
      </div>
    </UDashboardPanel>

    <!-- Advanced Search Modal -->
    <LogsAdvancedSearch
      :show="showAdvancedSearch"
      :conditions="{
        range: logsFilter.range,
        ...logsSubFilter
      }"
      @close="showAdvancedSearch = false"
      @search="onAdvancedSearch"
    />
  </UDashboardPage>
</template>
